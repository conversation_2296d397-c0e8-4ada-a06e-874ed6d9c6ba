// src/app/core/services/supabase.service.spec.ts
import { TestBed } from '@angular/core/testing';
import { SupabaseService } from './supabase.service';

describe('SupabaseService', () => {
  let service: SupabaseService;
  let mockSupabaseClient: any;

  beforeEach(() => {
    // Mock Supabase client
    mockSupabaseClient = {
      auth: {
        onAuthStateChange: jasmine.createSpy('onAuthStateChange').and.returnValue({
          data: { subscription: { unsubscribe: jasmine.createSpy() } }
        }),
        getSession: jasmine.createSpy('getSession').and.returnValue(
          Promise.resolve({ data: { session: null }, error: null })
        ),
        signInWithPassword: jasmine.createSpy('signInWithPassword'),
        signUp: jasmine.createSpy('signUp'),
        signOut: jasmine.createSpy('signOut'),
        setSession: jasmine.createSpy('setSession')
      },
      from: jasmine.createSpy('from').and.returnValue({
        select: jasmine.createSpy('select').and.returnValue({
          eq: jasmine.createSpy('eq').and.returnValue({
            single: jasmine.createSpy('single').and.returnValue(
              Promise.resolve({ data: null, error: null })
            )
          })
        }),
        insert: jasmine.createSpy('insert').and.returnValue({
          select: jasmine.createSpy('select').and.returnValue({
            single: jasmine.createSpy('single').and.returnValue(
              Promise.resolve({ data: null, error: null })
            )
          })
        }),
        update: jasmine.createSpy('update').and.returnValue({
          eq: jasmine.createSpy('eq').and.returnValue({
            select: jasmine.createSpy('select').and.returnValue({
              single: jasmine.createSpy('single').and.returnValue(
                Promise.resolve({ data: null, error: null })
              )
            })
          })
        }),
        delete: jasmine.createSpy('delete').and.returnValue({
          eq: jasmine.createSpy('eq').and.returnValue(
            Promise.resolve({ data: null, error: null })
          )
        })
      })
    };

    TestBed.configureTestingModule({
      providers: [SupabaseService]
    });

    service = TestBed.inject(SupabaseService);
    
    // Replace the actual Supabase client with our mock
    service.supabaseInstance = mockSupabaseClient;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize with null user state', () => {
    expect(service.currentUser()).toBeNull();
    expect(service.isAuthenticated()).toBeFalse();
  });

  it('should provide auth instance', () => {
    expect(service.auth).toBe(mockSupabaseClient.auth);
  });

  describe('Profile Management', () => {
    it('should return null for invalid user ID', async () => {
      const result = await service.getProfile('');
      expect(result).toBeNull();
    });

    it('should call Supabase for valid user ID', async () => {
      const userId = 'test-user-id';
      await service.getProfile(userId);
      
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('profiles');
    });
  });

  describe('Authentication State', () => {
    it('should handle auth state changes', () => {
      // Verify that auth state change listener is set up
      expect(mockSupabaseClient.auth.onAuthStateChange).toHaveBeenCalled();
    });

    it('should provide current session observable', () => {
      expect(service.currentSession$).toBeDefined();
    });

    it('should provide current user observable', () => {
      expect(service.currentUser$).toBeDefined();
    });
  });

  describe('Time Entry Management', () => {
    it('should handle time entry creation', async () => {
      const mockTimeEntry = {
        user_id: 'test-user',
        entry_type: 'REGULAR_WORK',
        start_time: new Date().toISOString(),
        end_time: new Date().toISOString(),
        post_number: '123',
        post_description: 'Test Post',
        notes: 'Test notes'
      };

      await service.addTimeEntry(mockTimeEntry);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith('time_entries');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Mock an error response
      mockSupabaseClient.from.and.returnValue({
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({ 
              data: null, 
              error: { message: 'Database error' } 
            })
          })
        })
      });

      try {
        await service.getProfile('test-user');
        fail('Should have thrown an error');
      } catch (error: any) {
        expect(error.message).toBe('Database error');
      }
    });
  });
});
