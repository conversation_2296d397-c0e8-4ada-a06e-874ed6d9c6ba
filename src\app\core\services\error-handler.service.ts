// src/app/core/services/error-handler.service.ts
import { Injectable, ErrorHandler, inject, signal } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { environment } from '../../../environments/environment';

export interface AppError {
  id: string;
  message: string;
  stack?: string | undefined;
  timestamp: Date;
  url?: string | undefined;
  userId?: string | undefined;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any> | undefined;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService implements ErrorHandler {
  private readonly snackBar = inject(MatSnackBar);

  // Signal-based error state
  readonly errors = signal<AppError[]>([]);
  readonly hasErrors = signal(false);

  handleError(error: any): void {
    const appError = this.createAppError(error);

    // Add to error collection
    this.errors.update(errors => [...errors, appError]);
    this.hasErrors.set(true);

    // Log error based on environment
    if (environment.logging.enableConsoleLogging || !environment.production) {
      console.error('Application Error:', appError);
    }

    // Show user-friendly message
    this.showUserNotification(appError);

    // Send to remote logging service in production
    if (environment.production && environment.logging.enableRemoteLogging) {
      this.sendToRemoteLogging(appError);
    }
  }

  private createAppError(error: any): AppError {
    const id = this.generateErrorId();
    const timestamp = new Date();
    const url = window.location.href;

    let message = 'An unexpected error occurred';
    let severity: AppError['severity'] = 'medium';
    let stack: string | undefined;

    if (error instanceof Error) {
      message = error.message;
      stack = error.stack;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // Determine severity based on error type
    if (error?.name === 'ChunkLoadError' || message.includes('Loading chunk')) {
      severity = 'low';
      message = 'Failed to load application resources. Please refresh the page.';
    } else if (message.includes('Network Error') || message.includes('fetch')) {
      severity = 'medium';
      message = 'Network connection issue. Please check your internet connection.';
    } else if (message.includes('Authentication') || message.includes('Unauthorized')) {
      severity = 'high';
      message = 'Authentication error. Please log in again.';
    } else if (message.includes('Database') || message.includes('Supabase')) {
      severity = 'critical';
      message = 'Database connection issue. Please try again later.';
    }

    return {
      id,
      message,
      stack,
      timestamp,
      url,
      severity,
      context: {
        userAgent: navigator.userAgent,
        timestamp: timestamp.toISOString(),
        environment: environment.production ? 'production' : 'development'
      }
    };
  }

  private showUserNotification(error: AppError): void {
    const duration = error.severity === 'critical' ? 10000 : 5000;

    this.snackBar.open(
      error.message,
      'Dismiss',
      {
        duration,
        panelClass: [`error-snackbar`, `error-${error.severity}`],
        horizontalPosition: 'center',
        verticalPosition: 'top'
      }
    );
  }

  private sendToRemoteLogging(error: AppError): void {
    // Implement remote logging service integration
    // This could be Sentry, LogRocket, or custom logging endpoint
    try {
      // Example implementation - replace with your logging service
      fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error)
      }).catch(logError => {
        console.warn('Failed to send error to remote logging:', logError);
      });
    } catch (logError) {
      console.warn('Failed to send error to remote logging:', logError);
    }
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public methods for manual error reporting
  reportError(message: string, context?: Record<string, any>, severity: AppError['severity'] = 'medium'): void {
    const error = new Error(message);
    const appError = this.createAppError(error);
    appError.severity = severity;
    appError.context = { ...appError.context, ...context };

    this.handleError(appError);
  }

  clearErrors(): void {
    this.errors.set([]);
    this.hasErrors.set(false);
  }

  getErrorById(id: string): AppError | undefined {
    return this.errors().find(error => error.id === id);
  }
}
