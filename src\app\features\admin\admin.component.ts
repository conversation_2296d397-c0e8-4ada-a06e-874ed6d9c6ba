// src/app/features/admin/admin.component.ts
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { SupabaseService, Profile, OtPeriod, HolidayEvent, PaydayDate, AppModule, AppStatistic } from '../../core/services/supabase.service';
import { FeatureFlagService } from '../../core/services/feature-flag.service';
import { DateUtilsService } from '../../core/services/date-utils.service';
import { User } from '@supabase/supabase-js';

// Angular Material Modules
import { MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core'; // Or your chosen date adapter
import { MatTableModule, MatTableDataSource } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';


@Component({
  selector: 'app-admin',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTableModule,
    MatButtonModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatChipsModule,
    MatSlideToggleModule,
    MatTooltipModule,
    MatSelectModule
  ],
  templateUrl: './admin.component.html',
  styleUrls: ['./admin.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AdminComponent implements OnInit, OnDestroy {
  // User and profile data
  currentUser: User | null = null;
  profile: Profile | null = null;
  isAdmin = false;

  // Loading and error states
  isLoading = true;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  // Data collections
  otPeriods: OtPeriod[] = [];
  holidayEvents: HolidayEvent[] = [];
  paydayDates: PaydayDate[] = [];
  appModules: AppModule[] = [];
  appStatistics: AppStatistic[] = [];

  // Active tab
  activeTab: 'ot-periods' | 'holidays' | 'paydays' | 'modules' | 'stats' = 'ot-periods';
  activeTabIndex = 0; // For MatTabGroup

  // Forms
  otPeriodForm: FormGroup;
  holidayEventForm: FormGroup;
  paydayDateForm: FormGroup;

  // Edit states
  editingOtPeriod: OtPeriod | null = null;
  editingHolidayEvent: HolidayEvent | null = null;
  editingPaydayDate: PaydayDate | null = null;

  // MatTable DataSources
  otPeriodsDataSource = new MatTableDataSource<OtPeriod>();
  holidayEventsDataSource = new MatTableDataSource<HolidayEvent>();
  paydayDatesDataSource = new MatTableDataSource<PaydayDate>();
  appModulesDataSource = new MatTableDataSource<AppModule>(); // Assuming AppModule is the correct type

  // Displayed Columns for MatTable
  displayedColumnsOtPeriods: string[] = ['display_name', 'start_date', 'end_date', 'actions'];
  displayedColumnsHolidays: string[] = ['event_date', 'event_name', 'actions'];
  displayedColumnsPaydays: string[] = ['payday_date', 'pay_period_start', 'pay_period_end', 'actions'];
  displayedColumnsModules: string[] = ['display_name', 'description', 'is_enabled', 'access_level', 'actions'];


  // Subscriptions
  private userSubscription: Subscription | null = null;
  private modulesSubscription: Subscription | null = null;

  constructor(
    private supabaseService: SupabaseService,
    private featureFlagService: FeatureFlagService,
    private dateUtils: DateUtilsService,
    private fb: FormBuilder,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {
    // Initialize forms
    this.otPeriodForm = this.fb.group({
      display_name: ['', Validators.required],
      start_date: ['', Validators.required],
      end_date: ['', Validators.required]
    }, { validators: this.dateRangeValidator });

    this.holidayEventForm = this.fb.group({
      event_date: ['', Validators.required],
      event_name: ['', Validators.required]
    });

    this.paydayDateForm = this.fb.group({
      payday_date: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.userSubscription = this.supabaseService.currentUser$.subscribe(user => {
      if (user && typeof user !== 'boolean') {
        this.currentUser = user;
        this.loadProfile(user.id);
      } else {
        this.handleNotAdmin();
      }
    });

    this.modulesSubscription = this.featureFlagService.modules$.subscribe(modules => {
      this.appModules = modules;
      this.appModulesDataSource.data = this.appModules; // Update datasource
      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.userSubscription?.unsubscribe();
    this.modulesSubscription?.unsubscribe();
  }

  async loadProfile(userId: string): Promise<void> {
    try {
      this.isLoading = true;
      this.cdr.markForCheck();

      const profile = await this.supabaseService.getProfile(userId);

      if (!profile || !profile.is_admin) {
        this.handleNotAdmin();
        return;
      }

      this.profile = profile;
      this.isAdmin = true;

      // Load initial data
      await this.loadData();

    } catch (error: any) {
      console.error('Error loading profile:', error);
      this.errorMessage = error.message || 'Failed to load profile';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  handleNotAdmin(): void {
    this.isAdmin = false;
    this.router.navigate(['/dashboard']);
  }

  async loadData(): Promise<void> {
    try {
      this.isLoading = true;
      this.cdr.markForCheck();

      // Load all data in parallel
      const [otPeriods, holidayEvents, paydayDates, appStatistics] = await Promise.all([
        this.supabaseService.getAllOtPeriods(),
        this.supabaseService.getHolidaysEvents(
          new Date(new Date().getFullYear() - 1, 0, 1).toISOString().split('T')[0]!,
          new Date(new Date().getFullYear() + 1, 11, 31).toISOString().split('T')[0]!
        ),
        this.supabaseService.getPaydayDates(
          new Date(new Date().getFullYear() - 1, 0, 1).toISOString().split('T')[0]!,
          new Date(new Date().getFullYear() + 1, 11, 31).toISOString().split('T')[0]!
        ),
        this.supabaseService.getAppStatistics()
      ]);

      this.otPeriods = otPeriods;
      this.otPeriodsDataSource.data = this.otPeriods; // Update datasource
      this.holidayEvents = holidayEvents;
      this.holidayEventsDataSource.data = this.holidayEvents; // Update datasource
      this.paydayDates = paydayDates;
      this.paydayDatesDataSource.data = this.paydayDates; // Update datasource
      this.appStatistics = appStatistics;

      // Reload modules
      await this.featureFlagService.loadModules(); // This already updates appModulesDataSource via subscription

    } catch (error: any) {
      console.error('Error loading data:', error);
      this.errorMessage = error.message || 'Failed to load data';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  // Tab navigation
  // setActiveTab(tab: 'ot-periods' | 'holidays' | 'paydays' | 'modules' | 'stats'): void {
  //   this.activeTab = tab;
  //   this.resetForms();
  //   this.cdr.markForCheck();
  // }
  handleTabChange(index: number): void {
    const tabs: Array<'ot-periods' | 'holidays' | 'paydays' | 'modules' | 'stats'> = ['ot-periods', 'holidays', 'paydays', 'modules', 'stats'];
    this.activeTab = tabs[index]!;
    this.activeTabIndex = index;
    this.resetForms();
    this.cdr.markForCheck();
  }


  // Reset all forms and edit states
  resetForms(): void {
    this.otPeriodForm.reset();
    this.holidayEventForm.reset();
    this.paydayDateForm.reset();
    this.editingOtPeriod = null;
    this.editingHolidayEvent = null;
    this.editingPaydayDate = null;
    this.errorMessage = null;
    this.successMessage = null;
  }

  // Form validators
  dateRangeValidator(formGroup: FormGroup): { [key: string]: boolean } | null {
    const startControl = formGroup.get('start_date') || formGroup.get('pay_period_start');
    const endControl = formGroup.get('end_date') || formGroup.get('pay_period_end');

    if (!startControl || !endControl) {
      return null;
    }

    const start = startControl.value;
    const end = endControl.value;

    if (!start || !end) {
      return null;
    }

    return start > end ? { 'endDateBeforeStart': true } : null;
  }

  // OT Period methods
  async onSubmitOtPeriod(): Promise<void> {
    if (this.otPeriodForm.invalid) {
      this.errorMessage = 'Please fill out the form correctly.';
      return;
    }

    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.successMessage = null;
      this.cdr.markForCheck();

      const formData = this.otPeriodForm.value;

      if (this.editingOtPeriod) {
        // Update existing OT period
        await this.supabaseService.updateOtPeriod({
          id: this.editingOtPeriod.id,
          display_name: formData.display_name,
          start_date: formData.start_date,
          end_date: formData.end_date
        });
        this.successMessage = 'OT Period updated successfully!';
      } else {
        // Add new OT period
        await this.supabaseService.addOtPeriod({
          display_name: formData.display_name,
          start_date: formData.start_date,
          end_date: formData.end_date
        });
        this.successMessage = 'OT Period added successfully!';
      }

      // Reload OT periods
      this.otPeriods = await this.supabaseService.getAllOtPeriods();
      this.otPeriodsDataSource.data = this.otPeriods; // Update datasource
      this.resetForms();
    } catch (error: any) {
      console.error('Error saving OT period:', error);
      this.errorMessage = error.message || 'Failed to save OT period';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  editOtPeriod(otPeriod: OtPeriod): void {
    this.editingOtPeriod = otPeriod;
    this.otPeriodForm.patchValue({
      display_name: otPeriod.display_name || '',
      start_date: otPeriod.start_date,
      end_date: otPeriod.end_date
    });
    this.cdr.markForCheck();
  }

  async deleteOtPeriod(id: number): Promise<void> {
    if (!confirm('Are you sure you want to delete this OT Period?')) {
      return;
    }

    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.cdr.markForCheck();

      await this.supabaseService.deleteOtPeriod(id);
      this.otPeriods = await this.supabaseService.getAllOtPeriods();
      this.otPeriodsDataSource.data = this.otPeriods; // Update datasource
      this.successMessage = 'OT Period deleted successfully!';
    } catch (error: any) {
      console.error('Error deleting OT period:', error);
      this.errorMessage = error.message || 'Failed to delete OT period';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  // Holiday/Event methods
  async onSubmitHolidayEvent(): Promise<void> {
    if (this.holidayEventForm.invalid) {
      this.errorMessage = 'Please fill out the form correctly.';
      return;
    }

    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.successMessage = null;
      this.cdr.markForCheck();

      const formData = this.holidayEventForm.value;

      // Ensure the date is preserved exactly as entered by the user
      // The date string from the form is in YYYY-MM-DD format
      // We use it directly without any time zone conversion
      const eventDate = formData.event_date;

      // Log the date for debugging purposes
      console.log('Holiday/Event date from form:', eventDate);

      if (this.editingHolidayEvent) {
        // Update existing holiday/event
        await this.supabaseService.updateHolidayEvent({
          id: this.editingHolidayEvent.id,
          event_date: eventDate,
          event_name: formData.event_name
        });
        this.successMessage = 'Holiday/Event updated successfully!';
      } else {
        // Add new holiday/event
        await this.supabaseService.addHolidayEvent({
          event_date: eventDate,
          event_name: formData.event_name
        });
        this.successMessage = 'Holiday/Event added successfully!';
      }

      // Reload holidays/events
      this.holidayEvents = await this.supabaseService.getHolidaysEvents(
        new Date(new Date().getFullYear() - 1, 0, 1).toISOString().split('T')[0]!,
        new Date(new Date().getFullYear() + 1, 11, 31).toISOString().split('T')[0]!
      );
      this.holidayEventsDataSource.data = this.holidayEvents; // Update datasource
      this.resetForms();
    } catch (error: any) {
      console.error('Error saving holiday/event:', error);
      this.errorMessage = error.message || 'Failed to save holiday/event';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  editHolidayEvent(holidayEvent: HolidayEvent): void {
    this.editingHolidayEvent = holidayEvent;
    this.holidayEventForm.patchValue({
      event_date: holidayEvent.event_date,
      event_name: holidayEvent.event_name
    });
    this.cdr.markForCheck();
  }

  async deleteHolidayEvent(id: number): Promise<void> {
    if (!confirm('Are you sure you want to delete this Holiday/Event?')) {
      return;
    }

    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.cdr.markForCheck();

      await this.supabaseService.deleteHolidayEvent(id);
      this.holidayEvents = await this.supabaseService.getHolidaysEvents(
        new Date(new Date().getFullYear() - 1, 0, 1).toISOString().split('T')[0]!,
        new Date(new Date().getFullYear() + 1, 11, 31).toISOString().split('T')[0]!
      );
      this.holidayEventsDataSource.data = this.holidayEvents; // Update datasource
      this.successMessage = 'Holiday/Event deleted successfully!';
    } catch (error: any) {
      console.error('Error deleting holiday/event:', error);
      this.errorMessage = error.message || 'Failed to delete holiday/event';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  // Payday Date methods
  async onSubmitPaydayDate(): Promise<void> {
    if (this.paydayDateForm.invalid) {
      this.errorMessage = 'Please fill out the form correctly.';
      return;
    }

    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.successMessage = null;
      this.cdr.markForCheck();

      const formData = this.paydayDateForm.value;

      // Use DateUtilsService to create a date from the payday_date string
      const paydayDate = this.dateUtils.fromFormDate(formData.payday_date);

      // Log the payday date for debugging
      console.log('Payday date from form:', formData.payday_date, 'parsed date:', paydayDate);

      // Calculate the first day of the month for the pay period start
      const payPeriodStart = new Date(paydayDate.getFullYear(), paydayDate.getMonth(), 1);

      // Calculate the last day of the month for the pay period end
      const payPeriodEnd = new Date(paydayDate.getFullYear(), paydayDate.getMonth() + 1, 0);

      // Format dates as YYYY-MM-DD using DateUtilsService
      const payPeriodStartStr = this.dateUtils.toDateString(payPeriodStart);
      const payPeriodEndStr = this.dateUtils.toDateString(payPeriodEnd);

      if (this.editingPaydayDate) {
        // Update existing payday date
        await this.supabaseService.updatePaydayDate({
          id: this.editingPaydayDate.id,
          payday_date: formData.payday_date,
          pay_period_start: payPeriodStartStr,
          pay_period_end: payPeriodEndStr
        });
        this.successMessage = 'Payday Date updated successfully!';
      } else {
        // Add new payday date
        await this.supabaseService.addPaydayDate({
          payday_date: formData.payday_date,
          pay_period_start: payPeriodStartStr,
          pay_period_end: payPeriodEndStr
        });
        this.successMessage = 'Payday Date added successfully!';
      }

      // Reload payday dates
      this.paydayDates = await this.supabaseService.getPaydayDates(
        new Date(new Date().getFullYear() - 1, 0, 1).toISOString().split('T')[0]!,
        new Date(new Date().getFullYear() + 1, 11, 31).toISOString().split('T')[0]!
      );
      this.paydayDatesDataSource.data = this.paydayDates; // Update datasource
      this.resetForms();
    } catch (error: any) {
      console.error('Error saving payday date:', error);
      this.errorMessage = error.message || 'Failed to save payday date';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  editPaydayDate(paydayDate: PaydayDate): void {
    this.editingPaydayDate = paydayDate;
    this.paydayDateForm.patchValue({
      payday_date: paydayDate.payday_date
    });
    this.cdr.markForCheck();
  }

  async deletePaydayDate(id: number): Promise<void> {
    if (!confirm('Are you sure you want to delete this Payday Date?')) {
      return;
    }

    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.cdr.markForCheck();

      await this.supabaseService.deletePaydayDate(id);
      this.paydayDates = await this.supabaseService.getPaydayDates(
        new Date(new Date().getFullYear() - 1, 0, 1).toISOString().split('T')[0]!,
        new Date(new Date().getFullYear() + 1, 11, 31).toISOString().split('T')[0]!
      );
      this.paydayDatesDataSource.data = this.paydayDates; // Update datasource
      this.successMessage = 'Payday Date deleted successfully!';
    } catch (error: any) {
      console.error('Error deleting payday date:', error);
      this.errorMessage = error.message || 'Failed to delete payday date';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  // Module management methods
  async toggleModuleEnabled(module: AppModule): Promise<void> {
    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.cdr.markForCheck();

      // Check if the change is allowed
      const canChangeResult = await new Promise<{canChange: boolean, message: string | null}>(resolve => {
        this.featureFlagService.canChangeModuleState(
          module.id,
          !module.is_enabled
        ).subscribe(result => {
          resolve(result);
        });
      });

      if (!canChangeResult.canChange) {
        this.errorMessage = canChangeResult.message || 'Cannot change module state';
        this.isLoading = false;
        this.cdr.markForCheck();
        return;
      }

      // Update the module
      await this.featureFlagService.updateModule({
        id: module.id,
        is_enabled: !module.is_enabled
      });

      this.successMessage = `Module "${module.display_name}" ${!module.is_enabled ? 'enabled' : 'disabled'} successfully!`;
    } catch (error: any) {
      console.error('Error toggling module:', error);
      this.errorMessage = error.message || 'Failed to toggle module';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  async toggleModuleAccessLevel(module: AppModule): Promise<void> {
    try {
      this.isLoading = true;
      this.errorMessage = null;
      this.cdr.markForCheck();

      // Toggle between 'basic' and 'premium'
      const newAccessLevel = module.access_level === 'basic' ? 'premium' : 'basic';

      // Update the module
      await this.featureFlagService.updateModule({
        id: module.id,
        access_level: newAccessLevel
      });

      this.successMessage = `Module "${module.display_name}" access level changed to ${newAccessLevel} successfully!`;
    } catch (error: any) {
      console.error('Error changing module access level:', error);
      this.errorMessage = error.message || 'Failed to change module access level';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  // Helper methods
  formatDate(dateString: string): string {
    // Use the DateUtilsService to format the date consistently
    return this.dateUtils.formatDate(dateString);
  }

  getStatValue(statName: string): string {
    const stat = this.appStatistics.find(s => s.stat_name === statName);
    if (!stat) return '0';

    if (statName === 'total_users' || statName === 'total_entries') {
      return stat.stat_value?.count?.toString() || '0';
    }

    if (statName === 'monthly_active_users') {
      // Get current month and year
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth();
      const currentYear = currentDate.getFullYear();

      // Get the monthly data from the stat value
      const monthlyData = stat.stat_value?.data || {};

      // Get the count for the current month, defaulting to 0 if not found
      const activeUsers = monthlyData[currentYear]?.[currentMonth] || 0;

      // Format month name
      const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];

      return `${activeUsers} (${monthNames[currentMonth]} ${currentYear})`;
    }

    return JSON.stringify(stat.stat_value);
  }
}
