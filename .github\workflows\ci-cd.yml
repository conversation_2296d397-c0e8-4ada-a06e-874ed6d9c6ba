name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  CACHE_KEY: node-modules-${{ hashFiles('**/package-lock.json') }}

jobs:
  # Quality Assurance
  quality:
    name: Code Quality & Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --prefer-offline --no-audit
      
    - name: Lint code
      run: npm run lint
      
    - name: Run unit tests
      run: npm run test -- --watch=false --browsers=ChromeHeadless --code-coverage
      
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        
    - name: Type check
      run: npm run build -- --dry-run
      
    - name: Security audit
      run: npm audit --audit-level=moderate

  # Build & Performance
  build:
    name: Build & Performance Check
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --prefer-offline --no-audit
      
    - name: Build application
      run: npm run build --configuration=production
      env:
        NODE_OPTIONS: --max_old_space_size=4096
        
    - name: Analyze bundle size
      run: |
        npm install -g webpack-bundle-analyzer
        npm run build -- --stats-json
        webpack-bundle-analyzer dist/electronic-time-book/stats.json --report dist/bundle-report.html --mode static
        
    - name: Check bundle size limits
      run: |
        BUNDLE_SIZE=$(du -sk dist/electronic-time-book | cut -f1)
        echo "Bundle size: ${BUNDLE_SIZE}KB"
        if [ $BUNDLE_SIZE -gt 1200 ]; then
          echo "Bundle size exceeds 1.2MB limit"
          exit 1
        fi
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: |
          dist/
          coverage/
        retention-days: 7

  # Security Scan
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # E2E Testing
  e2e:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci --prefer-offline --no-audit
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts
        
    - name: Run E2E tests
      run: npm run e2e
      env:
        CYPRESS_baseUrl: http://localhost:4200

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts
        
    - name: Deploy to DigitalOcean Staging
      uses: digitalocean/app_action@v1.1.5
      with:
        app_name: etb-staging
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, security, e2e]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts
        
    - name: Deploy to DigitalOcean Production
      uses: digitalocean/app_action@v1.1.5
      with:
        app_name: electronic-time-book
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
        
    - name: Create GitHub Release
      if: success()
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        body: |
          Automated release from main branch
          
          Changes in this release:
          ${{ github.event.head_commit.message }}
        draft: false
        prerelease: false

  # Notify on failure
  notify:
    name: Notify on Failure
    runs-on: ubuntu-latest
    needs: [quality, build, security]
    if: failure()
    
    steps:
    - name: Send notification
      run: |
        echo "Pipeline failed for commit ${{ github.sha }}"
        # Add notification logic here (Slack, email, etc.)
