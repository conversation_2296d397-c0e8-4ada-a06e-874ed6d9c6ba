// src/environments/environment.ts
export const environment = {
  production: false,
  supabaseUrl: 'https://focdpldclhthtrotjcbd.supabase.co',
  supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZvY2RwbGRjbGh0aHRyb3RqY2JkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYxNTY5NzEsImV4cCI6MjA2MTczMjk3MX0.G4vTExS1jCCTl4Q0OkWmr3Nuza_3VYG9s9DtbP0re54',

  // Application Configuration
  appName: 'Electronic Time Book',
  version: '1.0.0-dev',

  // Feature Flags
  features: {
    enableAnalytics: false,
    enableErrorReporting: true,
    enablePerformanceMonitoring: true,
    enableOfflineMode: false,
    enablePushNotifications: false
  },

  // API Configuration
  api: {
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000
  },

  // Security Configuration
  security: {
    enableCSP: false,
    enableSRI: false,
    sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000 // 15 minutes
  },

  // Performance Configuration
  performance: {
    enableServiceWorker: false,
    enableLazyLoading: true,
    enablePreloading: true,
    cacheTimeout: 24 * 60 * 60 * 1000 // 24 hours
  },

  // Logging Configuration
  logging: {
    level: 'debug',
    enableConsoleLogging: true,
    enableRemoteLogging: false
  }
};