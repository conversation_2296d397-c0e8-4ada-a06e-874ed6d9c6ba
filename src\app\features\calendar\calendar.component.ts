// src/app/features/calendar/calendar.component.ts
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { startOfMonth, endOfMonth, format, formatISO } from 'date-fns';
import { Subscription } from 'rxjs';
import { SupabaseService, TimeEntry, HolidayEvent, PaydayDate, OtPeriod } from '../../core/services/supabase.service';
import { DateUtilsService } from '../../core/services/date-utils.service';
import { User } from '@supabase/supabase-js';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

// FullCalendar CSS is imported in the component styles

// Import FullCalendar core and plugins
import { FullCalendarModule, FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';

// Import event detail component
import { EventDetailComponent } from './event-detail/event-detail.component';
import { AddEntryDialogComponent } from './add-entry-dialog/add-entry-dialog.component';

// Define colors
const colors: Record<string, string> = {
  red: '#ad2121',
  blue: '#1e90ff',
  yellow: '#e3bc08',
  green: '#006400',
  orange: '#FF8C00',
  purple: '#800080',
  grey: '#6c757d',
  teal: '#008080',
  gold: '#FFD700',
  darkBlue: '#00008B'
};

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [CommonModule, FullCalendarModule, MatSnackBarModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss']
})
export class CalendarComponent implements OnInit, OnDestroy {
  @ViewChild('calendar') calendarComponent!: FullCalendarComponent;

  calendarOptions: CalendarOptions = {
    plugins: [dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin],
    initialView: 'dayGridMonth',
    headerToolbar: {
      left: 'prev,next space today', // Added space
      center: 'title',
      right: 'dayGridMonth space timeGridWeek space timeGridDay space listMonth' // Added spaces
    },
    events: [],
    eventClick: this.handleEventClick.bind(this),
    dateClick: this.handleDateClick.bind(this),
    select: this.handleDateSelect.bind(this),
    selectable: true,
    dayMaxEventRows: 3, // Limit the number of events displayed per day
    // Disable selection in month view by setting selectable based on view type
    views: {
      dayGridMonth: {
        selectable: false // Disable selection in month view
      },
      timeGridWeek: {
        selectable: true // Enable selection in week view
      },
      timeGridDay: {
        selectable: true // Enable selection in day view
      }
    },
    editable: false,
    height: 'auto'
  };

  isLoading = true;
  errorMessage: string | null = null;
  currentUser: User | null = null;
  userSubscription: Subscription | null = null;
  isMobile: boolean = false;

  constructor(
    private supabaseService: SupabaseService,
    private dateUtils: DateUtilsService,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Check for mobile screen to adjust calendar options
    this.isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

    if (this.isMobile) {
      this.calendarOptions.buttonText = {
        prev: '<',
        next: '>',
        today: 'T',
        dayGridMonth: 'M',
        timeGridWeek: 'W',
        timeGridDay: 'D',
        listMonth: 'L'
      };
      this.calendarOptions.headerToolbar = {
        left: 'prev,next',
        center: 'title',
        right: 'today dayGridMonth,listMonth' // Simplified right section
      };
    }

    this.userSubscription = this.supabaseService.currentUser$.subscribe(user => {
      if (user && typeof user !== 'boolean') {
        this.currentUser = user;

        // Set up calendar view change handler to ensure data is loaded when month changes
        this.calendarOptions.datesSet = (info) => {
          console.log('Calendar view changed to:', info.view.title);
          this.fetchCalendarData();
        };

        // Initial data fetch
        this.fetchCalendarData();
      } else {
        this.currentUser = null;
        this.calendarOptions.events = [];
        this.isLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

  ngOnDestroy(): void {
    this.userSubscription?.unsubscribe();
  }

  async fetchCalendarData(): Promise<void> {
    if (!this.currentUser) {
      this.isLoading = false;
      this.cdr.markForCheck();
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    // Get current calendar date from FullCalendar API
    const calendarApi = this.calendarComponent?.getApi();
    const currentDate = calendarApi ? calendarApi.getDate() : new Date();

    console.log('Fetching calendar data for current date:', currentDate);

    // Calculate start and end of month
    const viewStart = startOfMonth(currentDate);
    const viewEnd = endOfMonth(currentDate);
    const viewStartDateStr = viewStart.toISOString().split('T')[0];
    const viewEndDateStr = viewEnd.toISOString().split('T')[0];

    try {
      // Expand the date range for holidays to ensure we get all holidays for the year
      // Use a wider range to ensure we capture all events
      const yearStart = this.dateUtils.toDateString(new Date(currentDate.getFullYear() - 1, 0, 1));
      const yearEnd = this.dateUtils.toDateString(new Date(currentDate.getFullYear() + 1, 11, 31));

      console.log('Fetching calendar data for date range:', viewStartDateStr, 'to', viewEndDateStr);
      console.log('Fetching holidays for date range:', yearStart, 'to', yearEnd);

      // Use the entire year for time entries to ensure we capture all entries
      // This ensures entries from other months are visible when navigating
      const currentYear = currentDate.getFullYear();
      const timeEntriesStart = new Date(currentYear, 0, 1); // January 1st of current year
      const timeEntriesEnd = new Date(currentYear, 11, 31); // December 31st of current year

      const timeEntriesStartStr = this.dateUtils.toDateString(timeEntriesStart);
      const timeEntriesEndStr = this.dateUtils.toDateString(timeEntriesEnd);

      console.log('Fetching time entries for range:', timeEntriesStartStr, 'to', timeEntriesEndStr);

      const [timeEntries, holidays, paydays, otPeriods] = await Promise.all([
        this.supabaseService.getTimeEntriesForUser(this.currentUser.id, timeEntriesStartStr, timeEntriesEndStr),
        this.supabaseService.getHolidaysEvents(yearStart, yearEnd),
        this.supabaseService.getPaydayDates(viewStartDateStr!, viewEndDateStr!),
        this.supabaseService.getAllOtPeriods()
      ]);

      // Convert time entries to FullCalendar events
      const timeEntryEvents = timeEntries.map((entry: TimeEntry) => {
        let backgroundColor = colors['blue']; // Default
        let prefix = '';

        // Assign colors based on entry type
        if (entry.entry_type === 'REGULAR_WORK') { backgroundColor = colors['green']; prefix = 'WORK'; }
        else if (entry.entry_type === 'OT_VOLUNTARY') { backgroundColor = colors['orange']; prefix = 'OT-V'; }
        else if (entry.entry_type === 'OT_MANDATED') { backgroundColor = colors['orange']; prefix = 'OT-M'; }
        else if (entry.entry_type === 'VACATION') { backgroundColor = colors['purple']; prefix = 'VAC'; }
        else if (entry.entry_type === 'SICK') { backgroundColor = colors['red']; prefix = 'SICK'; }
        else if (entry.entry_type === 'ANNUAL_LEAVE') { backgroundColor = colors['purple']; prefix = 'AL'; }
        // Add other types as needed

        // Format times as military time (HHMM) without colons
        const startDate = new Date(entry.start_time);
        const startHours = startDate.getHours().toString().padStart(2, '0');
        const startMinutes = startDate.getMinutes().toString().padStart(2, '0');
        const startTimeFormatted = `${startHours}${startMinutes}`;
        const title = `${startTimeFormatted} ${prefix}`;

        return {
          id: entry.id?.toString(),
          title: title,
          start: new Date(entry.start_time),
          end: new Date(entry.end_time),
          backgroundColor: backgroundColor,
          borderColor: backgroundColor,
          extendedProps: {
            type: 'time-entry',
            entry: entry
          }
        };
      });

      // Convert holidays to FullCalendar events
      console.log('Holidays fetched:', holidays);
      const holidayEvents = holidays.map((holiday: HolidayEvent) => {
        // Use DateUtilsService to create a date from the event_date string
        const eventDate = this.dateUtils.fromFormDate(holiday.event_date);

        console.log('Processing holiday:', holiday.event_name, 'date:', holiday.event_date, 'parsed date:', eventDate);

        return {
          id: `holiday-${holiday.id}`,
          title: `✨ ${holiday.event_name}`, // Add glowing star icon
          start: eventDate,
          allDay: true,
          backgroundColor: colors['blue'], // Changed to blue as requested
          borderColor: colors['blue'],
          textColor: '#FFFFFF', // White text for better contrast on blue
          display: 'block', // Make it more visible
          classNames: ['holiday-event', 'blue-glow'], // Add custom classes for styling
          extendedProps: {
            type: 'holiday',
            holiday: holiday
          },
          interactive: false,
          editable: false, // Prevent editing
          selectable: false // Prevent selection
        };
      });

      // Convert paydays to FullCalendar events
      const paydayEvents = paydays.map((payday: PaydayDate) => {
        // Use DateUtilsService to create a date from the payday_date string
        const paydayDate = this.dateUtils.fromFormDate(payday.payday_date);

        return {
          id: `payday-${payday.id}`,
          title: 'Payday 💰',
          start: paydayDate,
          allDay: true,
          backgroundColor: colors['gold'],
          borderColor: colors['gold'],
          textColor: '#000000',
          display: 'block', // Make it more visible
          classNames: ['payday-event', 'gold-glow'], // Add custom classes for styling
          extendedProps: {
            type: 'payday',
            payday: payday
          },
          interactive: false
        };
      });

      // Convert OT periods to FullCalendar events (start and end dates)
      const otPeriodEvents: any[] = [];

      otPeriods.forEach((period: OtPeriod) => {
        // Start date event - use DateUtilsService to create a date from the start_date string
        const startDate = this.dateUtils.fromFormDate(period.start_date);
        // Use display_name if available, otherwise fall back to month name
        const displayName = period.display_name || format(startDate, 'MMM');

        otPeriodEvents.push({
          id: `ot-start-${period.id}`,
          title: `🟢 OT Begin: ${displayName}`,
          start: startDate,
          allDay: true,
          backgroundColor: colors['teal'],
          borderColor: colors['teal'],
          extendedProps: {
            type: 'ot-period',
            otPeriod: period
          },
          interactive: false
        });

        // End date event - use DateUtilsService to create a date from the end_date string
        const endDate = this.dateUtils.fromFormDate(period.end_date);
        // Use display_name if available, otherwise fall back to month name
        const endDisplayName = period.display_name || format(endDate, 'MMM');

        otPeriodEvents.push({
          id: `ot-end-${period.id}`,
          title: `🔴 OT End: ${endDisplayName}`,
          start: endDate,
          allDay: true,
          backgroundColor: colors['teal'],
          borderColor: colors['teal'],
          extendedProps: {
            type: 'ot-period',
            otPeriod: period
          },
          interactive: false
        });
      });

      // Update calendar events
      const allEvents = [
        ...timeEntryEvents,
        ...holidayEvents,
        ...paydayEvents,
        ...otPeriodEvents
      ];

      console.log('All calendar events:', allEvents);
      console.log('Holiday events count:', holidayEvents.length);

      // Set up event rendering to add data attributes for CSS targeting
      this.calendarOptions.eventDidMount = (info) => {
        const eventType = info.event.extendedProps['type'];
        if (eventType) {
          info.el.setAttribute('data-event-type', eventType);
        }
        // Add title attribute for hover tooltip
        info.el.setAttribute('title', info.event.title);

        // If it's mobile and an 'ot-period' event, show full title on tap
        if (this.isMobile && info.event.extendedProps['type'] === 'ot-period') {
          info.el.addEventListener('click', () => {
            this.snackBar.open(info.event.title, 'Dismiss', {
              duration: 3000, // Snackbar dismisses after 3 seconds
              horizontalPosition: 'center',
              verticalPosition: 'bottom'
            });
          });
        }
      };

      this.calendarOptions.events = allEvents;

      // Force calendar to re-render
      if (calendarApi) {
        calendarApi.refetchEvents();
      }

    } catch (error: any) {
      console.error("Error fetching calendar data:", error);
      this.errorMessage = error.message || "Failed to load calendar data.";
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  // Event handlers for FullCalendar
  handleEventClick(info: any): void {
    const eventData = info.event;
    const extendedProps = eventData.extendedProps;

    // Admin-created entries are now non-interactive, so they won't trigger this handler
    // This is just a fallback in case something goes wrong
    if (extendedProps.type === 'holiday' || extendedProps.type === 'ot-period' || extendedProps.type === 'payday') {
      return;
    }

    const isHoliday = extendedProps.type === 'holiday';

    // Open dialog to view/edit event details
    this.dialog.open(EventDetailComponent, {
      width: '400px',
      data: {
        event: eventData,
        isHoliday: isHoliday,
        isReadOnly: false
      }
    }).afterClosed().subscribe(result => {
      if (result) {
        // Refresh calendar data if changes were made
        this.fetchCalendarData();
      }
    });
  }

  handleDateClick(info: any): void {
    // Open dialog to add a new entry for the clicked date
    console.log('Date click info:', info);

    // Get the date directly from the calendar event
    // This is the most reliable way to get the exact date that was clicked
    const clickedDate = info.date;
    console.log('Clicked date object:', clickedDate);

    // Create a JavaScript Date object for the clicked date
    // We'll pass this directly to the dialog component
    const date = new Date(clickedDate);

    // Log the date for debugging
    console.log('Date object to pass to dialog:', date);
    console.log('Date string representation:', date.toISOString());

    // Prepare time if it's a time grid view
    let startTime = null;
    if (info.view.type.includes('timeGrid')) {
      startTime = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }

    if (!this.currentUser) {
      this.snackBar.open('You must be logged in to add entries', 'Dismiss', { duration: 3000 });
      return;
    }

    // Open the dialog with the clicked date
    this.dialog.open(AddEntryDialogComponent, {
      width: '550px',
      maxWidth: '95vw',
      panelClass: 'app-dialog-container',
      disableClose: false, // Allow closing by clicking outside
      autoFocus: false, // Prevent autofocus which can cause flickering
      data: {
        date: date, // Pass the date object directly
        startTime: startTime,
        currentUser: this.currentUser
      }
    }).afterClosed().subscribe(result => {
      if (result) {
        // Refresh calendar data if an entry was added
        this.fetchCalendarData();
      }
    });
  }

  handleDateSelect(info: any): void {
    // Open dialog to add a new entry for the selected date range
    const startDate = info.start;
    const endDate = info.end;

    // Create a JavaScript Date object for the selected date
    const date = new Date(startDate);
    console.log('Date object from selection:', date);

    // Format start and end times if this is a time selection
    let startTime = null;
    let endTime = null;

    if (info.view.type.includes('timeGrid')) {
      startTime = `${startDate.getHours().toString().padStart(2, '0')}:${startDate.getMinutes().toString().padStart(2, '0')}`;
      endTime = `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`;
    }

    if (!this.currentUser) {
      this.snackBar.open('You must be logged in to add entries', 'Dismiss', { duration: 3000 });
      return;
    }

    console.log('Date object to pass to dialog (from selection):', date);

    // Open the dialog with the selected date range
    this.dialog.open(AddEntryDialogComponent, {
      width: '550px',
      maxWidth: '95vw',
      panelClass: 'app-dialog-container',
      disableClose: false, // Allow closing by clicking outside
      autoFocus: false, // Prevent autofocus which can cause flickering
      data: {
        date: date, // Pass the date object directly
        startTime: startTime,
        endTime: endTime,
        currentUser: this.currentUser
      }
    }).afterClosed().subscribe(result => {
      if (result) {
        // Refresh calendar data if an entry was added
        this.fetchCalendarData();
      }
    });
  }
}