{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"electronic-time-book": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/electronic-time-book", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["node_modules"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1.5MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "8kB", "maximumError": "16kB"}], "outputHashing": "all", "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "electronic-time-book:build:production"}, "development": {"buildTarget": "electronic-time-book:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": []}}}}}}