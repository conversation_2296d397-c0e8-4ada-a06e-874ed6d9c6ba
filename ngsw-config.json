{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/manifest.webmanifest", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(eot|svg|cur|jpg|png|webp|gif|otf|ttf|woff|woff2|ani)"]}}], "dataGroups": [{"name": "api-performance", "urls": ["https://focdpldclhthtrotjcbd.supabase.co/rest/v1/profiles*", "https://focdpldclhthtrotjcbd.supabase.co/rest/v1/app_modules*", "https://focdpldclhthtrotjcbd.supabase.co/rest/v1/holidays_events*", "https://focdpldclhthtrotjcbd.supabase.co/rest/v1/payday_dates*", "https://focdpldclhthtrotjcbd.supabase.co/rest/v1/ot_periods*"], "cacheConfig": {"strategy": "performance", "maxSize": 100, "maxAge": "1h", "timeout": "10s"}}, {"name": "api-freshness", "urls": ["https://focdpldclhthtrotjcbd.supabase.co/rest/v1/time_entries*", "https://focdpldclhthtrotjcbd.supabase.co/rest/v1/app_statistics*"], "cacheConfig": {"strategy": "freshness", "maxSize": 50, "maxAge": "5m", "timeout": "5s"}}], "navigationUrls": ["/**", "!/**/*.*", "!/**/*__*", "!/**/*__*/**"]}