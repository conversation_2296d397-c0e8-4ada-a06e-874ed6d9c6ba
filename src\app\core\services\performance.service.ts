// src/app/core/services/performance.service.ts
import { Injectable, signal, computed } from '@angular/core';
import { environment } from '../../../environments/environment';

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: Date;
  type: 'navigation' | 'resource' | 'custom' | 'vitals';
  details?: Record<string, any> | undefined;
}

export interface VitalMetrics {
  fcp: number | null; // First Contentful Paint
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  ttfb: number | null; // Time to First Byte
}

@Injectable({
  providedIn: 'root'
})
export class PerformanceService {
  // Signal-based performance state
  readonly metrics = signal<PerformanceMetric[]>([]);
  readonly vitals = signal<VitalMetrics>({
    fcp: null,
    lcp: null,
    fid: null,
    cls: null,
    ttfb: null
  });

  readonly isMonitoring = signal(environment.features.enablePerformanceMonitoring);

  // Computed performance insights
  readonly averageLoadTime = computed(() => {
    const loadMetrics = this.metrics().filter(m => m.name === 'page_load');
    if (loadMetrics.length === 0) return 0;
    return loadMetrics.reduce((sum, m) => sum + m.value, 0) / loadMetrics.length;
  });

  readonly performanceScore = computed(() => {
    const vitalsData = this.vitals();
    let score = 100;

    // Deduct points based on Core Web Vitals
    if (vitalsData.lcp && vitalsData.lcp > 2500) score -= 20;
    if (vitalsData.fid && vitalsData.fid > 100) score -= 20;
    if (vitalsData.cls && vitalsData.cls > 0.1) score -= 20;
    if (vitalsData.fcp && vitalsData.fcp > 1800) score -= 15;
    if (vitalsData.ttfb && vitalsData.ttfb > 600) score -= 15;

    return Math.max(0, score);
  });

  private observer?: PerformanceObserver;
  private navigationStartTime = performance.now();

  constructor() {
    if (this.isMonitoring()) {
      this.initializePerformanceMonitoring();
    }
  }

  private initializePerformanceMonitoring(): void {
    // Monitor navigation timing
    this.measureNavigationTiming();

    // Monitor Core Web Vitals
    this.measureCoreWebVitals();

    // Monitor resource loading
    this.measureResourceTiming();

    // Monitor long tasks
    this.measureLongTasks();

    // Monitor memory usage
    this.measureMemoryUsage();
  }

  private measureNavigationTiming(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

      if (navigation) {
        this.recordMetric('page_load', navigation.loadEventEnd - navigation.fetchStart, 'navigation', {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          domInteractive: navigation.domInteractive - navigation.fetchStart,
          domComplete: navigation.domComplete - navigation.fetchStart
        });

        // Update TTFB
        this.vitals.update(vitals => ({
          ...vitals,
          ttfb: navigation.responseStart - navigation.fetchStart
        }));
      }
    });
  }

  private measureCoreWebVitals(): void {
    // First Contentful Paint
    this.observePerformanceEntries('paint', (entries) => {
      const fcp = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcp) {
        this.vitals.update(vitals => ({ ...vitals, fcp: fcp.startTime }));
        this.recordMetric('first_contentful_paint', fcp.startTime, 'vitals');
      }
    });

    // Largest Contentful Paint
    this.observePerformanceEntries('largest-contentful-paint', (entries) => {
      const lcp = entries[entries.length - 1];
      if (lcp) {
        this.vitals.update(vitals => ({ ...vitals, lcp: lcp.startTime }));
        this.recordMetric('largest_contentful_paint', lcp.startTime, 'vitals');
      }
    });

    // First Input Delay
    this.observePerformanceEntries('first-input', (entries) => {
      const fid = entries[0] as any; // Type assertion for first-input entries
      if (fid && fid.processingStart) {
        const fidValue = fid.processingStart - fid.startTime;
        this.vitals.update(vitals => ({ ...vitals, fid: fidValue }));
        this.recordMetric('first_input_delay', fidValue, 'vitals');
      }
    });

    // Cumulative Layout Shift
    this.observePerformanceEntries('layout-shift', (entries) => {
      let clsValue = 0;
      entries.forEach(entry => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });

      this.vitals.update(vitals => ({ ...vitals, cls: clsValue }));
      this.recordMetric('cumulative_layout_shift', clsValue, 'vitals');
    });
  }

  private measureResourceTiming(): void {
    this.observePerformanceEntries('resource', (entries) => {
      entries.forEach(entry => {
        const resource = entry as PerformanceResourceTiming;
        const loadTime = resource.responseEnd - resource.startTime;

        this.recordMetric(`resource_${this.getResourceType(resource)}`, loadTime, 'resource', {
          name: resource.name,
          size: resource.transferSize,
          cached: resource.transferSize === 0
        });
      });
    });
  }

  private measureLongTasks(): void {
    this.observePerformanceEntries('longtask', (entries) => {
      entries.forEach(entry => {
        this.recordMetric('long_task', entry.duration, 'custom', {
          startTime: entry.startTime,
          attribution: (entry as any).attribution
        });
      });
    });
  }

  private measureMemoryUsage(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.recordMetric('memory_used', memory.usedJSHeapSize, 'custom', {
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        });
      }, 30000); // Every 30 seconds
    }
  }

  private observePerformanceEntries(
    type: string,
    callback: (entries: PerformanceEntry[]) => void
  ): void {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });

      observer.observe({ type, buffered: true });
    } catch (error) {
      console.warn(`Performance observer for ${type} not supported:`, error);
    }
  }

  private getResourceType(resource: PerformanceResourceTiming): string {
    const url = new URL(resource.name);
    const extension = url.pathname.split('.').pop()?.toLowerCase();

    if (['js', 'mjs'].includes(extension || '')) return 'script';
    if (['css'].includes(extension || '')) return 'stylesheet';
    if (['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(extension || '')) return 'image';
    if (['woff', 'woff2', 'ttf', 'otf'].includes(extension || '')) return 'font';
    if (resource.name.includes('api') || resource.name.includes('supabase')) return 'api';

    return 'other';
  }

  private recordMetric(
    name: string,
    value: number,
    type: PerformanceMetric['type'],
    details?: Record<string, any>
  ): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: new Date(),
      type,
      details
    };

    this.metrics.update(metrics => [...metrics.slice(-99), metric]); // Keep last 100 metrics

    // Log performance issues
    if (this.isPerformanceIssue(metric)) {
      console.warn('Performance Issue Detected:', metric);

      if (environment.production) {
        this.reportPerformanceIssue(metric);
      }
    }
  }

  private isPerformanceIssue(metric: PerformanceMetric): boolean {
    switch (metric.name) {
      case 'page_load':
        return metric.value > 3000; // 3 seconds
      case 'largest_contentful_paint':
        return metric.value > 2500; // 2.5 seconds
      case 'first_input_delay':
        return metric.value > 100; // 100ms
      case 'cumulative_layout_shift':
        return metric.value > 0.1; // 0.1
      case 'long_task':
        return metric.value > 50; // 50ms
      default:
        return false;
    }
  }

  private reportPerformanceIssue(metric: PerformanceMetric): void {
    try {
      fetch('/api/performance/issues', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...metric,
          userAgent: navigator.userAgent,
          url: window.location.href,
          connection: (navigator as any).connection?.effectiveType
        })
      }).catch(error => {
        console.warn('Failed to report performance issue:', error);
      });
    } catch (error) {
      console.warn('Failed to report performance issue:', error);
    }
  }

  // Public methods
  measureCustomMetric(name: string, startTime?: number): void {
    const endTime = performance.now();
    const value = startTime ? endTime - startTime : endTime - this.navigationStartTime;

    this.recordMetric(name, value, 'custom');
  }

  getPerformanceReport() {
    return {
      vitals: this.vitals(),
      averageLoadTime: this.averageLoadTime(),
      performanceScore: this.performanceScore(),
      recentMetrics: this.metrics().slice(-20),
      recommendations: this.getPerformanceRecommendations()
    };
  }

  private getPerformanceRecommendations(): string[] {
    const recommendations: string[] = [];
    const vitalsData = this.vitals();

    if (vitalsData.lcp && vitalsData.lcp > 2500) {
      recommendations.push('Optimize Largest Contentful Paint by reducing image sizes and server response times');
    }

    if (vitalsData.fid && vitalsData.fid > 100) {
      recommendations.push('Reduce First Input Delay by minimizing JavaScript execution time');
    }

    if (vitalsData.cls && vitalsData.cls > 0.1) {
      recommendations.push('Improve Cumulative Layout Shift by setting dimensions for images and ads');
    }

    if (this.averageLoadTime() > 3000) {
      recommendations.push('Optimize page load time by implementing code splitting and lazy loading');
    }

    return recommendations;
  }

  clearMetrics(): void {
    this.metrics.set([]);
  }
}
