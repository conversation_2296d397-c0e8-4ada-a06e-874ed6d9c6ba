{"name": "Electronic Time Book", "short_name": "ETB", "description": "Digital time tracking for CDCR Correctional Officers", "theme_color": "#2e7d32", "background_color": "#ffffff", "display": "standalone", "scope": "/", "start_url": "/", "orientation": "portrait-primary", "categories": ["productivity", "business", "utilities"], "lang": "en-US", "dir": "ltr", "icons": [{"src": "/assets/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/assets/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "Add Time Entry", "short_name": "Add Entry", "description": "Quickly add a new time entry", "url": "/calendar", "icons": [{"src": "/assets/icons/add-entry-96x96.png", "sizes": "96x96"}]}, {"name": "View Dashboard", "short_name": "Dashboard", "description": "View your time tracking dashboard", "url": "/dashboard", "icons": [{"src": "/assets/icons/dashboard-96x96.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/assets/screenshots/desktop-dashboard.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Dashboard view on desktop"}, {"src": "/assets/screenshots/mobile-calendar.png", "sizes": "375x812", "type": "image/png", "form_factor": "narrow", "label": "Calendar view on mobile"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}