import { TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { SupabaseService } from './core/services/supabase.service';
import { Router } from '@angular/router';
import { signal } from '@angular/core';
import { of } from 'rxjs';

describe('AppComponent', () => {
  let mockSupabaseService: jasmine.SpyObj<SupabaseService>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    // Create spy objects for dependencies
    mockSupabaseService = jasmine.createSpyObj('SupabaseService', ['auth'], {
      currentUser$: of(null),
      currentSession$: of(null)
    });

    mockRouter = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [AppComponent],
      providers: [
        { provide: SupabaseService, useValue: mockSupabaseService },
        { provide: Router, useValue: mockRouter }
      ]
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it(`should have the 'Electronic Time Book' title signal`, () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app.title()).toEqual('Electronic Time Book');
  });

  it('should initialize with mobile detection', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app.isMobile()).toBeDefined();
    expect(typeof app.isMobile()).toBe('boolean');
  });
});
