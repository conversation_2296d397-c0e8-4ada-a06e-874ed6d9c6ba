# DigitalOcean App Platform Configuration
# Electronic Time Book - Production Deployment

name: electronic-time-book
region: nyc

# Static Site Configuration
static_sites:
- name: etb-frontend
  source_dir: /
  github:
    repo: Harelin/electronic-time-book
    branch: main
    deploy_on_push: true
  
  # Build Configuration
  build_command: |
    npm ci --production=false
    npm run build --configuration=production
  
  output_dir: /dist/electronic-time-book/browser
  
  # Environment Variables
  envs:
  - key: NODE_ENV
    value: production
    type: SECRET
  - key: SUPABASE_URL
    value: https://focdpldclhthtrotjcbd.supabase.co
    type: SECRET
  - key: SUPABASE_ANON_KEY
    value: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZvY2RwbGRjbGh0aHRyb3RqY2JkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYxNTY5NzEsImV4cCI6MjA2MTczMjk3MX0.G4vTExS1jCCTl4Q0OkWmr3Nuza_3VYG9s9DtbP0re54
    type: SECRET
  
  # HTTP Configuration
  http_port: 8080
  
  # Routes Configuration
  routes:
  - path: /
  
  # Error Pages
  error_document: /index.html
  
  # Headers for Security
  headers:
  - name: X-Frame-Options
    value: DENY
  - name: X-Content-Type-Options
    value: nosniff
  - name: X-XSS-Protection
    value: "1; mode=block"
  - name: Strict-Transport-Security
    value: "max-age=31536000; includeSubDomains"
  - name: Content-Security-Policy
    value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://focdpldclhthtrotjcbd.supabase.co wss://focdpldclhthtrotjcbd.supabase.co"

# Health Checks
health_check:
  http_path: /
  initial_delay_seconds: 30
  period_seconds: 60
  timeout_seconds: 10
  success_threshold: 1
  failure_threshold: 3

# Resource Allocation
instance_count: 1
instance_size_slug: basic-xxs

# Alerts
alerts:
- rule: CPU_UTILIZATION
  value: 80
- rule: MEM_UTILIZATION
  value: 80
- rule: RESTART_COUNT
  value: 5
