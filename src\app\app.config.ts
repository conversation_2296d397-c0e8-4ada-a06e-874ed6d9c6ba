// src/app/app.config.ts
import { ApplicationConfig, importProvidersFrom, ErrorHandler } from '@angular/core';
import { provideRouter, withHashLocation, withComponentInputBinding, withViewTransitions, withPreloading, PreloadAllModules } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient, withInterceptorsFromDi, withFetch } from '@angular/common/http';
import { provideExperimentalZonelessChangeDetection } from '@angular/core';
import { provideServiceWorker } from '@angular/service-worker';

import { routes } from './app.routes';
import { environment } from '../environments/environment';

// Import FullCalendar modules
import { FullCalendarModule } from '@fullcalendar/angular';

// Import Material modules
import { MatDialogModule } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSnackBarModule } from '@angular/material/snack-bar';

// Import custom services
import { ErrorHandlerService } from './core/services/error-handler.service';
import { SecurityService } from './core/services/security.service';
import { PerformanceService } from './core/services/performance.service';

export const appConfig: ApplicationConfig = {
  providers: [
    // Modern router configuration with enhanced features
    provideRouter(
      routes,
      withHashLocation(),
      withComponentInputBinding(), // Enable automatic input binding from route params
      withViewTransitions(), // Enable view transitions for better UX
      withPreloading(PreloadAllModules) // Preload all lazy-loaded modules
    ),

    // Modern HTTP client with fetch API and interceptor support
    provideHttpClient(
      withFetch(), // Use modern fetch API instead of XMLHttpRequest
      withInterceptorsFromDi() // Support for DI-based interceptors
    ),

    // Enhanced animations
    provideAnimations(),

    // Service Worker for offline functionality and caching
    provideServiceWorker('/sw.js', {
      enabled: environment.production && environment.performance.enableServiceWorker,
      registrationStrategy: 'registerWhenStable:30000'
    }),

    // Optional: Enable experimental zoneless change detection for better performance
    // Uncomment when ready to migrate away from Zone.js
    // provideExperimentalZonelessChangeDetection(),

    // Custom error handler
    { provide: ErrorHandler, useClass: ErrorHandlerService },

    // Initialize core services
    SecurityService,
    PerformanceService,

    // Import FullCalendar and Material modules globally
    importProvidersFrom(
      FullCalendarModule,
      MatDialogModule,
      MatDatepickerModule,
      MatNativeDateModule,
      MatSnackBarModule
    )
  ]
};