// src/app/core/services/security.service.ts
import { Injectable, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import { SupabaseService } from './supabase.service';
import { environment } from '../../../environments/environment';

export interface SecurityEvent {
  type: 'login_attempt' | 'failed_login' | 'session_timeout' | 'suspicious_activity';
  timestamp: Date;
  details: Record<string, any>;
  severity: 'low' | 'medium' | 'high';
}

@Injectable({
  providedIn: 'root'
})
export class SecurityService {
  private readonly router = inject(Router);
  private readonly supabaseService = inject(SupabaseService);

  // Security state signals
  readonly isSecurityEnabled = signal(environment.security.enableCSP);
  readonly failedLoginAttempts = signal(0);
  readonly isAccountLocked = signal(false);
  readonly sessionTimeoutWarning = signal(false);
  readonly securityEvents = signal<SecurityEvent[]>([]);

  private sessionTimeoutTimer?: number;
  private warningTimeoutTimer?: number;
  private lockoutTimer?: number;

  constructor() {
    this.initializeSecurityMonitoring();
    this.setupSessionTimeout();
  }

  private initializeSecurityMonitoring(): void {
    // Monitor authentication state changes
    this.supabaseService.currentUser$.subscribe(user => {
      if (user && typeof user !== 'boolean') {
        this.onSuccessfulLogin();
      } else if (user === false) {
        this.onLogout();
      }
    });

    // Monitor for suspicious activity
    this.setupSuspiciousActivityDetection();
  }

  private setupSessionTimeout(): void {
    const timeoutDuration = environment.security.sessionTimeout;
    const warningDuration = timeoutDuration - (5 * 60 * 1000); // 5 minutes before timeout

    // Clear existing timers
    this.clearSessionTimers();

    // Set warning timer
    this.warningTimeoutTimer = window.setTimeout(() => {
      this.sessionTimeoutWarning.set(true);
      this.logSecurityEvent('session_timeout', { stage: 'warning' }, 'low');
    }, warningDuration);

    // Set session timeout timer
    this.sessionTimeoutTimer = window.setTimeout(() => {
      this.handleSessionTimeout();
    }, timeoutDuration);
  }

  private clearSessionTimers(): void {
    if (this.sessionTimeoutTimer) {
      clearTimeout(this.sessionTimeoutTimer);
    }
    if (this.warningTimeoutTimer) {
      clearTimeout(this.warningTimeoutTimer);
    }
  }

  private handleSessionTimeout(): void {
    this.logSecurityEvent('session_timeout', { reason: 'automatic_timeout' }, 'medium');
    this.sessionTimeoutWarning.set(false);
    this.supabaseService.auth.signOut();
    this.router.navigate(['/auth/login'], { 
      queryParams: { reason: 'session_timeout' } 
    });
  }

  extendSession(): void {
    this.sessionTimeoutWarning.set(false);
    this.setupSessionTimeout();
    this.logSecurityEvent('session_timeout', { action: 'extended' }, 'low');
  }

  onLoginAttempt(email: string): void {
    this.logSecurityEvent('login_attempt', { email }, 'low');
  }

  onFailedLogin(email: string, reason: string): void {
    const attempts = this.failedLoginAttempts() + 1;
    this.failedLoginAttempts.set(attempts);

    this.logSecurityEvent('failed_login', { 
      email, 
      reason, 
      attempts,
      ip: this.getClientIP()
    }, attempts >= 3 ? 'high' : 'medium');

    // Lock account after max attempts
    if (attempts >= environment.security.maxLoginAttempts) {
      this.lockAccount();
    }
  }

  private onSuccessfulLogin(): void {
    this.failedLoginAttempts.set(0);
    this.isAccountLocked.set(false);
    this.clearLockoutTimer();
    this.setupSessionTimeout();
  }

  private onLogout(): void {
    this.clearSessionTimers();
    this.sessionTimeoutWarning.set(false);
  }

  private lockAccount(): void {
    this.isAccountLocked.set(true);
    
    this.logSecurityEvent('suspicious_activity', { 
      type: 'account_locked',
      reason: 'max_login_attempts_exceeded'
    }, 'high');

    // Auto-unlock after lockout duration
    this.lockoutTimer = window.setTimeout(() => {
      this.unlockAccount();
    }, environment.security.lockoutDuration);
  }

  private unlockAccount(): void {
    this.isAccountLocked.set(false);
    this.failedLoginAttempts.set(0);
    this.clearLockoutTimer();
  }

  private clearLockoutTimer(): void {
    if (this.lockoutTimer) {
      clearTimeout(this.lockoutTimer);
    }
  }

  private setupSuspiciousActivityDetection(): void {
    // Monitor for rapid page navigation
    let navigationCount = 0;
    const navigationWindow = 10000; // 10 seconds

    this.router.events.subscribe(() => {
      navigationCount++;
      
      setTimeout(() => {
        navigationCount--;
      }, navigationWindow);

      // Flag suspicious navigation patterns
      if (navigationCount > 20) {
        this.logSecurityEvent('suspicious_activity', {
          type: 'rapid_navigation',
          count: navigationCount
        }, 'medium');
      }
    });

    // Monitor for console access (development detection)
    if (environment.production) {
      this.detectConsoleAccess();
    }
  }

  private detectConsoleAccess(): void {
    const devtools = {
      open: false,
      orientation: null as string | null
    };

    const threshold = 160;

    setInterval(() => {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools.open) {
          devtools.open = true;
          this.logSecurityEvent('suspicious_activity', {
            type: 'devtools_opened'
          }, 'low');
        }
      } else {
        devtools.open = false;
      }
    }, 500);
  }

  private logSecurityEvent(
    type: SecurityEvent['type'], 
    details: Record<string, any>, 
    severity: SecurityEvent['severity']
  ): void {
    const event: SecurityEvent = {
      type,
      timestamp: new Date(),
      details: {
        ...details,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString()
      },
      severity
    };

    this.securityEvents.update(events => [...events.slice(-99), event]); // Keep last 100 events

    // Log to console in development
    if (!environment.production) {
      console.warn('Security Event:', event);
    }

    // Send to remote monitoring in production
    if (environment.production && severity === 'high') {
      this.reportSecurityEvent(event);
    }
  }

  private reportSecurityEvent(event: SecurityEvent): void {
    // Send security events to monitoring service
    try {
      fetch('/api/security/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event)
      }).catch(error => {
        console.warn('Failed to report security event:', error);
      });
    } catch (error) {
      console.warn('Failed to report security event:', error);
    }
  }

  private getClientIP(): string {
    // This would typically be handled by the backend
    // For client-side, we can only get limited info
    return 'client-side-unknown';
  }

  // Public methods
  getSecurityStatus() {
    return {
      isSecurityEnabled: this.isSecurityEnabled(),
      failedLoginAttempts: this.failedLoginAttempts(),
      isAccountLocked: this.isAccountLocked(),
      sessionTimeoutWarning: this.sessionTimeoutWarning(),
      recentEvents: this.securityEvents().slice(-10)
    };
  }

  clearSecurityEvents(): void {
    this.securityEvents.set([]);
  }
}
